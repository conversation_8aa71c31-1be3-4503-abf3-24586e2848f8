import { Button, Row, Form, Input, DatePicker } from 'antd';
import React, { useState, useRef, useEffect, useMemo } from 'react';

import EnumSelector from 'vcomponents/Form/EnumSelector';
import RestSelector from 'vcomponents/Form/Rest/RestSelector';
import DateDuration from 'vcomponents/Date/DateDuration';
import moment from 'moment';
import { Content, MainContent, ActionContent, ColWrapper } from './styles';

interface IOptionBarProps {
  template;
  loading?: boolean;
  defaultValues?: object;
  // eslint-disable-next-line no-unused-vars
  onClick: (v: any) => object;
  onChange?: (v: any) => void;
  colInRow?: number;
}
// id, label, type: 'enum', data: { key1: value1, key2: value2... }
const renderRow = (props) => {
  const {
    id,
    label,
    type,
    data,
    required = false,
    defaultValue,
    onChange,
    defaultLabel,
    props: componentProps = {}
  } = props;
  let content = null;

  const inputEl = useRef(null);
  useEffect(() => {
    if (!inputEl.current) return;
    inputEl.current.input.setAttribute('size', 13);
  }, [inputEl]);

  const { restApi, translate, mapping, ...other } = componentProps;
  switch (type) {
    case 'enum':
      content = (
        <EnumSelector
          id={id}
          data={data}
          onChange={onChange}
          defaultValue={defaultValue}
          defaultLabel={defaultLabel}
          {...componentProps}
        />
      );
      break;

    case 'rest-selector':
      content = (
        <RestSelector id={id} queryFunc={restApi} translate={translate} mapping={mapping}>
          <EnumSelector
            allowNumStringKey
            id={id}
            onChange={onChange}
            value={defaultValue}
            defaultLabel={defaultLabel}
            {...other}
          />
        </RestSelector>
      );
      break;

    case 'date-duration':
      content = <DateDuration onChange={onChange} {...componentProps} />;
      break;

    case 'year':
      content = (
        <DatePicker
          allowClear={false}
          picker="year"
          clear
          defaultValue={defaultValue || moment()}
          onChange={onChange}
          {...componentProps}
        />
      );
      break;

    case 'string':
      content = (
        <Input ref={inputEl} id={id} onChange={onChange} defaultValue={defaultValue} style={{}} {...componentProps} />
      );
      break;

    case 'component':
      content = React.cloneElement(data, { onChange, ...componentProps });
      break;
    default:
      break;
  }

  return (
    <Form.Item
      name={label}
      label={label}
      rules={[
        {
          required,
          message: 'Input something!'
        }
      ]}
    >
      {content}
    </Form.Item>
  );
};

const OptionBar: React.FC<IOptionBarProps> = (props) => {
  const { template = [], onClick = () => {}, colInRow = 3, defaultValues = {}, loading = false } = props;
  const [_data, setData] = useState({ ...defaultValues });

  useMemo(() => {
    const values = {};
    // eslint-disable-next-line array-callback-return
    template.map((row) => {
      if (row.defaultValue) {
        values[row.id] = row.defaultValue;
      }
    });
    setData({ ..._data, ...values });
  }, [template]);

  const _onClick = () => {
    onClick(_data);
  };

  const _onItemChange = (id, v) => {
    let val = v;
    if (v && v.target) {
      val = v.target.value;
    }
    props.onChange?.({ ..._data, [id]: val });
    setData((old) => {
      return { ...old, [id]: val };
    });
  };

  return (
    <Content>
      <Form style={{ display: 'flex', flex: 1 }} onFinish={_onClick}>
        <MainContent>
          <Row style={{ marginRight: 0, flex: 1 }}>
            {template.map((row, i) => {
              const { id, span } = row;
              const dv = defaultValues[id];
              const width = span ? Math.floor(24 / colInRow) * span : Math.floor(24 / colInRow);

              return (
                // eslint-disable-next-line react/no-array-index-key
                <ColWrapper span={width} key={i}>
                  {renderRow({ ...row, defaultValue: dv, onChange: (v) => _onItemChange(id, v) })}
                </ColWrapper>
              );
            })}
          </Row>
        </MainContent>
        <ActionContent>
          <Button type="primary" shape="round" htmlType="submit" block loading={loading}>
            검색
          </Button>
        </ActionContent>
      </Form>
    </Content>
  );
};
/*
// id, enum Selector
// id, label,
// id, custom Selector
interface MemoInputProps {
    value: any;
    update: number;
    children: React.ReactNode;
}

const MemoInput = React.memo(
    ({ children }: MemoInputProps) => children as JSX.Element,
    (prev, next) => {
      return prev.value === next.value && prev.update === next.update;
    },
);

const renderLayout = (
    baseChildren: React.ReactNode,
    fieldId?: string,
    meta?: any,
    isRequired?: boolean,
): React.FC =>{

    return <Row>
        {baseChildren}
    </Row>
}

const OptionBar:React.FC<IOptionBarProps> = (props) =>{
    var { children } = props;

    const childProps = { ...children.props, ...mergedControl };

    const mergedControl: typeof control = {
        // ...control,
      };

    let childNode: React.ReactNode = null;

        childNode = (
            <MemoInput
            value={mergedControl[props.valuePropName || 'value']}
            update={updateRef.current}
            >
            {React.cloneElement(children, childProps)}
            </MemoInput>
        );

    return renderLayout(childNode, fieldId, meta, isRequired);
}
*/
export default OptionBar;
