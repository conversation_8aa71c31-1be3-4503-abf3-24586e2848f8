import styled from 'styled-components';
import Spin from 'vcomponents/Spin';
import { Space } from 'antd';

const color = {
  red: 'red',
  orange: 'orange'
};

const statusColorMap = {
  RESERVE: color.orange,
  MINUS: color.red,
  GROUP_RESET: color.red,
  WITHRAW_RESET: color.red,
  POLICY_RESET: color.red
};

export const Gap = styled.div`
  .ant-space.ant-space-vertical {
    gap: 0px !important;
  }
`;

export const Container = styled(Spin)`
  //
`;

export const TabContainer = styled.div`
  //

  //border: 1px solid;

  & .ui.tabular.fluid.menu {
    width: 100% !important;
  }
  & .ui.tabular.menu.vendysFullTab .item {
    border-top: 3px solid #eeeeee;
  }
  & .info-form .title.policy-title .red.warning.circle.icon {
    margin-top: -10px;
  }
`;

export const ButtonArea = styled.div`
  display: flex;
  gap: 8px;
  padding-left: 12px;
`;

export const StatusCell = styled.div`
  color: ${(props) => (props.status === 'RESERVE' ? color.orange : null)};
`;

export const WelfarePointCell = styled.div`
  color: ${(props) => statusColorMap[props.status]};
`;

export const GroupCell = styled.div`
  //display: block;
`;

export const ExecuteCell = styled.div`
  color: ${(props) => (props.status === 'RESERVE' ? color.orange : null)};
`;

export const CauseCell = styled.div`
  color: ${(props) => statusColorMap[props.status]};
`;

export const ExcelGiveDeductContainer = styled.div`
  //width: 100%;
  & .body-content {
    margin-top: ${(props) => props.theme.rowPadding4}px;
    position: relative;
    & .export-result-excel {
      position: absolute;
      top: -13px;
      right: 0;
    }
  }
  & .final-page {
    border-top: 1px solid #dcdcdc;
  }
`;

export const StepContainer = styled(Space)`
  width: 100%;
  border: 1px solid #e1e4e6;
  padding: ${(props) => props.theme.padding * 2}px;
  & .download-excel {
    border-bottom: none;
  }
  & .complete {
    width: 200px;
  }
`;

export const StepDivide = styled.div`
  & h3 {
    font-size: 17px;
  }
`;
