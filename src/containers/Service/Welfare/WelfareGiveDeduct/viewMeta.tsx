import React from 'react';
import moment from 'moment';
import restMeta from 'constants/restMeta';
import { number as NumberFormat, datetime_str as DateTimeFormat } from 'decorators/text';
import commons from 'helpers/commons';
import { StatusCell, WelfarePointCell, ExecuteCell, CauseCell } from './styles';

export const filterColumns: any[] = [
  {
    id: 'date',
    type: 'date-duration',
    span: 2.5,
    props: { sdate: moment().startOf('month') }
  },
  {
    id: 'code',
    type: 'rest-selector',
    props: {
      ...restMeta.welfare.taskStatus,
      existTotal: true,
      totalText: '상태 전체',
      totalValue: '',
      defaultValue: ''
    },
    span: 0.5
  },
  {
    id: 'groupid',
    type: 'rest-selector',
    props: {
      ...restMeta.welfare.group,
      existTotal: true,
      totalText: '복지그룹 전체',
      totalValue: '',
      defaultValue: ''
    },
    span: 0.5
  },
  { id: 'keyword', type: 'string', props: { placeholder: '사용자명, 신청자, 사유' }, span: 0.5 }
];

export const giveDeductFilterColumns = [
  {
    id: 'welfareGroupIdx',
    type: 'rest-selector',
    props: {
      ...restMeta.welfare.group,
      existTotal: true,
      totalText: '복지그룹 전체',
      totalValue: '',
      defaultValue: ''
    },
    span: 0.5
  },
  {
    id: 'captainPaymentGrade',
    type: 'enum',
    props: {
      enumId: 'UserPermission',
      existTotal: true,
      totalText: '권한 전체',
      totalValue: '',
      defaultValue: ''
    },
    span: 0.5
  },
  { id: 'keyword', type: 'string', props: { placeholder: 'ID, 이름, 사원번호' }, span: 1 }
];

export const columns = [
  {
    HeaderId: '#',
    accessor: 'idx',
    width: 60
  },
  {
    HeaderId: 'status',
    accessor: 'status',
    Cell: ({
      row: {
        original: { status }
      }
    }) => {
      const { value, text } = status;
      return <StatusCell status={value}>{text}</StatusCell>;
    },
    width: 60
  },
  {
    HeaderId: 'welfare.point',
    accessor: 'amount',
    type: 'number',
    Cell: ({
      row: {
        original: { status, amount }
      }
    }) => {
      const { value } = status;
      return (
        <WelfarePointCell status={value}>
          <NumberFormat prefix={amount < 0 ? '(-) ' : ''} value={Math.abs(amount)} />
        </WelfarePointCell>
      );
    }
  },
  {
    HeaderId: 'group.name',
    accessor: 'group',
    Cell: ({
      row: {
        original: { group }
      }
    }) => {
      const { name } = group;
      return name;
    }
  },
  {
    HeaderId: 'welfare.give.name',
    accessor: 'user',
    viewId: 'string'
  },
  {
    HeaderId: 'welfare.give.register',
    accessor: 'register',
    viewId: 'datetime_str',
    width: 140
  },
  {
    HeaderId: 'welfare.give.execute',
    accessor: 'execute',
    Cell: ({
      row: {
        original: { status, execute }
      }
    }) => {
      const { value } = status;
      return (
        <ExecuteCell status={value}>
          <DateTimeFormat value={execute} />
        </ExecuteCell>
      );
    },
    width: 140
  },
  {
    HeaderId: 'welfare.give.total.count',
    accessor: 'total',
    viewId: 'number',
    width: 80
  },
  {
    HeaderId: 'welfare.give.success.count',
    accessor: 'success',
    viewId: 'number',
    width: 80
  },
  {
    HeaderId: 'welfare.give.fail.count',
    accessor: 'fail',
    viewId: 'number',
    width: 80
  },
  {
    HeaderId: 'welfare.give.cause',
    accessor: 'cause',
    viewProps: { style: { textAlign: 'center' } },
    Cell: ({
      row: {
        original: { cause, status }
      }
    }) => {
      const { text } = cause;
      return (
        <div
          style={{
            textAlign: 'center'
          }}
        >
          <CauseCell status={status?.value}>{text}</CauseCell>
        </div>
      );
    }
  }
];

export const popupChangedColumns = [
  { Header: '#', accessor: 'infoHistoryIdx' },
  { Header: '날짜', accessor: 'created' },
  { Header: '변경된 사용자', accessor: 'reservedSignId' },
  { Header: '변경 항목', accessor: 'typeName' },
  { Header: '상세내역', accessor: 'phone' },
  { Header: '상태', accessor: 'statusName' },
  { Header: '변경한 사람', accessor: 'createdUserName' }
];

export const intiDefaultData = filterColumns.reduce((a, e) => ({ ...a, [e.id]: e.props?.defaultValue }), {});

export const memberColumns = [
  {
    HeaderId: 'ID',
    accessor: 'signid',
    width: 120
  },
  {
    HeaderId: 'user.name',
    accessor: 'name',
    width: 116
  },
  {
    HeaderId: 'user.welfare.group',
    accessor: 'group',
    Cell: ({
      row: {
        original: { group }
      }
    }) => {
      return group?.name || '';
    },
    width: 128
  },
  {
    HeaderId: 'user.employee.com.id.number',
    accessor: 'comidnum',
    width: 128
  },
  {
    HeaderId: 'auth',
    accessor: 'grade',
    Cell: ({
      row: {
        original: { grade }
      }
    }) => {
      return grade?.text || '';
    },
    width: 88
  }
];

export const givenDeductColumn = commons.tableHeaderColumns(memberColumns, 2, '부서', null, { allowTooltip: true });
